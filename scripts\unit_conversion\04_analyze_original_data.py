#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析原始EEG数据的单位和分布
对比原始数据与预处理后数据的差异

作者：AI助手
日期：2025年1月
"""

import os
import numpy as np
import mne
from pathlib import Path
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class OriginalDataAnalyzer:
    """原始数据分析器"""

    def __init__(self):
        self.original_dir = Path(r"C:\数据\0-5-2矫正正式被试\2-0 eeg set")
        self.processed_dir = Path(r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突")
        self.results = []

    def analyze_original_set_file(self, set_path):
        """
        分析原始.set文件

        Parameters:
        -----------
        set_path : Path
            .set文件路径

        Returns:
        --------
        dict : 分析结果
        """
        try:
            print(f"分析原始文件: {set_path.name}")

            # 读取EEGLAB .set文件
            raw = mne.io.read_raw_eeglab(set_path, preload=True, verbose=False)

            # 获取基本信息
            n_channels = len(raw.ch_names)
            n_samples = raw.n_times
            sfreq = raw.info['sfreq']
            duration = n_samples / sfreq

            print(f"  通道数: {n_channels}")
            print(f"  采样点数: {n_samples}")
            print(f"  采样率: {sfreq} Hz")
            print(f"  时长: {duration:.2f} 秒")

            # 获取数据
            data = raw.get_data()

            # 分析数据单位和幅度
            data_stats = self.analyze_data_amplitude(data, raw.ch_names)

            # 检测可能的单位
            detected_unit = self.detect_unit_from_amplitude(data_stats['overall']['overall_amplitude'])

            result = {
                'file_name': set_path.name,
                'file_path': str(set_path),
                'n_channels': n_channels,
                'n_samples': n_samples,
                'sfreq': sfreq,
                'duration': duration,
                'data_stats': data_stats,
                'detected_unit': detected_unit,
                'channel_names': raw.ch_names[:10],  # 前10个通道名称
                'success': True
            }

            return result

        except Exception as e:
            print(f"  分析失败: {str(e)}")
            return {
                'file_name': set_path.name,
                'success': False,
                'error': str(e)
            }

    def analyze_data_amplitude(self, data, channel_names):
        """
        分析数据幅度特征

        Parameters:
        -----------
        data : ndarray
            数据矩阵
        channel_names : list
            通道名称列表

        Returns:
        --------
        dict : 幅度统计信息
        """
        # 整体统计
        overall_stats = {
            'mean': np.mean(data),
            'std': np.std(data),
            'min': np.min(data),
            'max': np.max(data),
            'amplitude_range': np.max(data) - np.min(data),
            'overall_amplitude': np.mean(np.std(data, axis=1))
        }

        # 按通道类型分析（如果能识别）
        eeg_indices = []
        ecg_indices = []

        for i, ch_name in enumerate(channel_names):
            ch_name_upper = ch_name.upper()
            if any(eeg_marker in ch_name_upper for eeg_marker in ['FP', 'F', 'C', 'P', 'O', 'T', 'AF', 'FC', 'CP', 'PO']):
                eeg_indices.append(i)
            elif any(ecg_marker in ch_name_upper for ecg_marker in ['ECG', 'EKG', 'HEART']):
                ecg_indices.append(i)

        channel_type_stats = {}

        if eeg_indices:
            eeg_data = data[eeg_indices, :]
            channel_type_stats['eeg'] = {
                'n_channels': len(eeg_indices),
                'mean': np.mean(eeg_data),
                'std': np.std(eeg_data),
                'min': np.min(eeg_data),
                'max': np.max(eeg_data),
                'amplitude_mean': np.mean(np.std(eeg_data, axis=1))
            }

        if ecg_indices:
            ecg_data = data[ecg_indices, :]
            channel_type_stats['ecg'] = {
                'n_channels': len(ecg_indices),
                'mean': np.mean(ecg_data),
                'std': np.std(ecg_data),
                'min': np.min(ecg_data),
                'max': np.max(ecg_data),
                'amplitude_mean': np.mean(np.std(ecg_data, axis=1))
            }

        print(f"  整体幅度统计:")
        print(f"    均值: {overall_stats['mean']:.6f}")
        print(f"    标准差: {overall_stats['std']:.6f}")
        print(f"    最小值: {overall_stats['min']:.6f}")
        print(f"    最大值: {overall_stats['max']:.6f}")
        print(f"    典型幅度: {overall_stats['overall_amplitude']:.6f}")

        if 'eeg' in channel_type_stats:
            print(f"  EEG通道 ({channel_type_stats['eeg']['n_channels']}个):")
            print(f"    典型幅度: {channel_type_stats['eeg']['amplitude_mean']:.6f}")

        if 'ecg' in channel_type_stats:
            print(f"  ECG通道 ({channel_type_stats['ecg']['n_channels']}个):")
            print(f"    典型幅度: {channel_type_stats['ecg']['amplitude_mean']:.6f}")

        return {
            'overall': overall_stats,
            'by_type': channel_type_stats
        }

    def detect_unit_from_amplitude(self, amplitude):
        """
        根据幅度检测可能的单位

        Parameters:
        -----------
        amplitude : float
            典型幅度值

        Returns:
        --------
        str : 检测到的单位
        """
        if amplitude > 1e-3:  # > 1mV
            return "V (伏特)"
        elif amplitude > 1e-6:  # > 1μV
            return "mV (毫伏)"
        else:
            return "μV (微伏)"

    def sample_analysis(self, n_files=3):
        """对部分原始文件进行抽样分析"""
        print("开始分析原始数据")
        print("="*60)

        # 获取.set文件
        set_files = list(self.original_dir.glob("*.set"))

        # 抽样分析
        sample_files = set_files[:n_files]

        print(f"选择 {len(sample_files)} 个文件进行分析")
        print()

        results = []
        for set_path in sample_files:
            result = self.analyze_original_set_file(set_path)
            results.append(result)
            print()

        return results

    def compare_with_processed_data(self):
        """与预处理后的数据进行对比"""
        print("对比原始数据与预处理后数据")
        print("="*50)

        # 分析原始数据
        original_results = self.sample_analysis(3)

        # 分析预处理后的数据（选择对应的文件）
        processed_results = []

        # 尝试找到对应的预处理文件
        for orig_result in original_results:
            if not orig_result['success']:
                continue

            # 提取被试编号
            orig_name = orig_result['file_name']
            subject_num = orig_name.split()[0]  # 获取编号部分

            # 查找对应的预处理文件
            processed_files = list(self.processed_dir.glob(f"{subject_num}_*_rest_*.fif"))
            if processed_files:
                processed_file = processed_files[0]  # 选择第一个rest文件
                processed_result = self.analyze_processed_fif(processed_file)
                processed_results.append(processed_result)

        # 生成对比报告
        self.generate_comparison_report(original_results, processed_results)

    def analyze_processed_fif(self, fif_path):
        """分析预处理后的fif文件"""
        try:
            print(f"分析预处理文件: {fif_path.name}")

            raw = mne.io.read_raw_fif(fif_path, preload=True, verbose=False)
            data = raw.get_data()

            # 分离EEG和ECG
            eeg_data = data[:61, :]
            ecg_data = data[61:, :] if data.shape[0] > 61 else None

            eeg_amplitude = np.mean(np.std(eeg_data, axis=1))
            ecg_amplitude = np.mean(np.std(ecg_data, axis=1)) if ecg_data is not None else None

            print(f"  EEG幅度: {eeg_amplitude:.6f}")
            if ecg_amplitude:
                print(f"  ECG幅度: {ecg_amplitude:.6f}")

            return {
                'file_name': fif_path.name,
                'eeg_amplitude': eeg_amplitude,
                'ecg_amplitude': ecg_amplitude,
                'success': True
            }

        except Exception as e:
            print(f"  分析失败: {str(e)}")
            return {'file_name': fif_path.name, 'success': False, 'error': str(e)}

    def generate_comparison_report(self, original_results, processed_results):
        """生成对比报告"""
        output_dir = Path("result/unit_conversion")
        output_dir.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = output_dir / f"original_vs_processed_comparison_{timestamp}.txt"

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("原始数据与预处理后数据对比分析报告\n")
            f.write("="*60 + "\n\n")
            f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("原始数据分析结果:\n")
            f.write("-"*30 + "\n")

            for result in original_results:
                if not result['success']:
                    continue

                f.write(f"文件: {result['file_name']}\n")
                f.write(f"  通道数: {result['n_channels']}\n")
                f.write(f"  采样率: {result['sfreq']} Hz\n")
                f.write(f"  检测单位: {result['detected_unit']}\n")

                overall_amp = result['data_stats']['overall']['overall_amplitude']
                f.write(f"  整体典型幅度: {overall_amp:.6f}\n")

                if 'eeg' in result['data_stats']['by_type']:
                    eeg_amp = result['data_stats']['by_type']['eeg']['amplitude_mean']
                    f.write(f"  EEG典型幅度: {eeg_amp:.6f}\n")

                if 'ecg' in result['data_stats']['by_type']:
                    ecg_amp = result['data_stats']['by_type']['ecg']['amplitude_mean']
                    f.write(f"  ECG典型幅度: {ecg_amp:.6f}\n")

                f.write("\n")

            f.write("预处理后数据分析结果:\n")
            f.write("-"*30 + "\n")

            for result in processed_results:
                if not result['success']:
                    continue

                f.write(f"文件: {result['file_name']}\n")
                f.write(f"  EEG幅度: {result['eeg_amplitude']:.6f} μV\n")
                if result['ecg_amplitude']:
                    f.write(f"  ECG幅度: {result['ecg_amplitude']:.6f} μV\n")
                f.write("\n")

            # 分析结论
            f.write("分析结论:\n")
            f.write("-"*30 + "\n")

            if original_results and processed_results:
                # 计算幅度变化
                orig_success = [r for r in original_results if r['success']]
                proc_success = [r for r in processed_results if r['success']]

                if orig_success and proc_success:
                    # 假设第一个文件进行对比
                    orig = orig_success[0]
                    proc = proc_success[0]

                    if 'eeg' in orig['data_stats']['by_type']:
                        orig_eeg = orig['data_stats']['by_type']['eeg']['amplitude_mean']
                        proc_eeg = proc['eeg_amplitude']

                        # 计算转换因子
                        conversion_factor = orig_eeg / proc_eeg if proc_eeg > 0 else 0

                        f.write(f"EEG信号幅度变化:\n")
                        f.write(f"  原始: {orig_eeg:.6f} (检测单位: {orig['detected_unit']})\n")
                        f.write(f"  预处理后: {proc_eeg:.6f} μV\n")
                        f.write(f"  变化倍数: {conversion_factor:.0f}x\n")

                        # 判断合理性
                        if conversion_factor > 500 and conversion_factor < 2000:
                            f.write(f"  结论: 转换合理，原始数据可能是mV单位\n")
                        elif conversion_factor > 500000:
                            f.write(f"  结论: 转换可能有问题，原始数据可能是V单位\n")
                        else:
                            f.write(f"  结论: 需要进一步检查转换逻辑\n")

        print(f"对比报告已保存: {report_path}")

def main():
    """主函数"""
    analyzer = OriginalDataAnalyzer()
    analyzer.compare_with_processed_data()

if __name__ == "__main__":
    main()
