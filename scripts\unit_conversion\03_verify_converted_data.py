#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证转换后的FIF数据质量
检查转换后的数据是否符合预期的生理学范围

作者：AI助手
日期：2025年1月
"""

import os
import numpy as np
import mne
from pathlib import Path
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def verify_single_file(fif_path):
    """
    验证单个转换后的fif文件
    
    Parameters:
    -----------
    fif_path : Path
        fif文件路径
        
    Returns:
    --------
    dict : 验证结果
    """
    try:
        print(f"验证文件: {fif_path.name}")
        
        # 读取数据
        raw = mne.io.read_raw_fif(fif_path, preload=True, verbose=False)
        data = raw.get_data()
        
        # 分离EEG和ECG数据
        eeg_data = data[:61, :]
        ecg_data = data[61:, :] if data.shape[0] > 61 else None
        
        # 计算统计信息
        eeg_stats = {
            'mean': np.mean(eeg_data),
            'std': np.std(eeg_data),
            'min': np.min(eeg_data),
            'max': np.max(eeg_data),
            'amplitude_mean': np.mean(np.std(eeg_data, axis=1)),
            'amplitude_range': np.max(eeg_data) - np.min(eeg_data)
        }
        
        ecg_stats = None
        if ecg_data is not None:
            ecg_stats = {
                'mean': np.mean(ecg_data),
                'std': np.std(ecg_data),
                'min': np.min(ecg_data),
                'max': np.max(ecg_data),
                'amplitude_mean': np.mean(np.std(ecg_data, axis=1)),
                'amplitude_range': np.max(ecg_data) - np.min(ecg_data)
            }
        
        # 生理学范围检查
        eeg_in_range = 1 <= eeg_stats['amplitude_mean'] <= 100  # 1-100 μV
        ecg_in_range = False
        if ecg_stats:
            ecg_in_range = 100 <= ecg_stats['amplitude_mean'] <= 5000  # 100-5000 μV
        
        print(f"  EEG幅度均值: {eeg_stats['amplitude_mean']:.2f} μV")
        print(f"  EEG在生理学范围内: {eeg_in_range}")
        if ecg_stats:
            print(f"  ECG幅度均值: {ecg_stats['amplitude_mean']:.2f} μV")
            print(f"  ECG在生理学范围内: {ecg_in_range}")
        
        return {
            'file_name': fif_path.name,
            'eeg_stats': eeg_stats,
            'ecg_stats': ecg_stats,
            'eeg_in_range': eeg_in_range,
            'ecg_in_range': ecg_in_range,
            'success': True
        }
        
    except Exception as e:
        print(f"  验证失败: {str(e)}")
        return {
            'file_name': fif_path.name,
            'success': False,
            'error': str(e)
        }

def sample_verification():
    """对部分文件进行抽样验证"""
    data_dir = Path(r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突")
    
    # 获取所有fif文件
    fif_files = list(data_dir.glob("*.fif"))
    
    # 抽样验证（选择前5个文件）
    sample_files = fif_files[:5]
    
    print(f"开始抽样验证，共选择 {len(sample_files)} 个文件")
    print("="*60)
    
    results = []
    for fif_path in sample_files:
        result = verify_single_file(fif_path)
        results.append(result)
        print()
    
    return results

def analyze_verification_results(results):
    """分析验证结果"""
    print("验证结果分析")
    print("="*40)
    
    successful_verifications = [r for r in results if r['success']]
    failed_verifications = [r for r in results if not r['success']]
    
    print(f"验证成功: {len(successful_verifications)} 个文件")
    print(f"验证失败: {len(failed_verifications)} 个文件")
    
    if successful_verifications:
        # EEG统计
        eeg_amplitudes = [r['eeg_stats']['amplitude_mean'] for r in successful_verifications]
        eeg_in_range_count = sum(1 for r in successful_verifications if r['eeg_in_range'])
        
        print(f"\nEEG信号分析:")
        print(f"  平均幅度范围: {min(eeg_amplitudes):.2f} - {max(eeg_amplitudes):.2f} μV")
        print(f"  在生理学范围内: {eeg_in_range_count}/{len(successful_verifications)} 个文件")
        
        # ECG统计
        ecg_results = [r for r in successful_verifications if r['ecg_stats'] is not None]
        if ecg_results:
            ecg_amplitudes = [r['ecg_stats']['amplitude_mean'] for r in ecg_results]
            ecg_in_range_count = sum(1 for r in ecg_results if r['ecg_in_range'])
            
            print(f"\nECG信号分析:")
            print(f"  平均幅度范围: {min(ecg_amplitudes):.2f} - {max(ecg_amplitudes):.2f} μV")
            print(f"  在生理学范围内: {ecg_in_range_count}/{len(ecg_results)} 个文件")
    
    if failed_verifications:
        print(f"\n验证失败的文件:")
        for r in failed_verifications:
            print(f"  {r['file_name']}: {r['error']}")

def create_verification_plot(results):
    """创建验证结果可视化"""
    successful_results = [r for r in results if r['success']]
    
    if not successful_results:
        print("没有成功的验证结果，无法生成图表")
        return
    
    # 提取数据
    eeg_amplitudes = [r['eeg_stats']['amplitude_mean'] for r in successful_results]
    ecg_amplitudes = [r['ecg_stats']['amplitude_mean'] for r in successful_results if r['ecg_stats']]
    
    # 创建图形
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle('转换后数据质量验证结果', fontsize=14, fontweight='bold')
    
    # EEG幅度分布
    axes[0].hist(eeg_amplitudes, bins=10, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0].axvspan(1, 100, alpha=0.2, color='green', label='生理学范围 (1-100 μV)')
    axes[0].set_xlabel('EEG信号幅度 (μV)')
    axes[0].set_ylabel('文件数量')
    axes[0].set_title('EEG信号幅度分布')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # ECG幅度分布
    if ecg_amplitudes:
        axes[1].hist(ecg_amplitudes, bins=10, alpha=0.7, color='lightcoral', edgecolor='black')
        axes[1].axvspan(100, 5000, alpha=0.2, color='green', label='生理学范围 (100-5000 μV)')
        axes[1].set_xlabel('ECG信号幅度 (μV)')
        axes[1].set_ylabel('文件数量')
        axes[1].set_title('ECG信号幅度分布')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
    else:
        axes[1].text(0.5, 0.5, '无ECG数据', ha='center', va='center', transform=axes[1].transAxes)
        axes[1].set_title('ECG信号幅度分布')
    
    plt.tight_layout()
    
    # 保存图形
    output_dir = Path("result/unit_conversion")
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    plot_path = output_dir / f"verification_results_{timestamp}.png"
    
    try:
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"验证结果图表已保存: {plot_path}")
    except Exception as e:
        print(f"保存图表时出错: {e}")
        plt.close()

def generate_verification_report(results):
    """生成验证报告"""
    output_dir = Path("result/unit_conversion")
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_path = output_dir / f"verification_report_{timestamp}.txt"
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("FIF数据单位转换验证报告\n")
        f.write("="*50 + "\n\n")
        f.write(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"验证文件数: {len(results)}\n\n")
        
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]
        
        f.write(f"验证成功: {len(successful_results)} 个文件\n")
        f.write(f"验证失败: {len(failed_results)} 个文件\n\n")
        
        if successful_results:
            # EEG分析
            eeg_amplitudes = [r['eeg_stats']['amplitude_mean'] for r in successful_results]
            eeg_in_range = sum(1 for r in successful_results if r['eeg_in_range'])
            
            f.write("EEG信号验证结果:\n")
            f.write(f"  幅度范围: {min(eeg_amplitudes):.3f} - {max(eeg_amplitudes):.3f} μV\n")
            f.write(f"  平均幅度: {np.mean(eeg_amplitudes):.3f} μV\n")
            f.write(f"  在生理学范围内: {eeg_in_range}/{len(successful_results)} ({eeg_in_range/len(successful_results)*100:.1f}%)\n\n")
            
            # ECG分析
            ecg_results = [r for r in successful_results if r['ecg_stats']]
            if ecg_results:
                ecg_amplitudes = [r['ecg_stats']['amplitude_mean'] for r in ecg_results]
                ecg_in_range = sum(1 for r in ecg_results if r['ecg_in_range'])
                
                f.write("ECG信号验证结果:\n")
                f.write(f"  幅度范围: {min(ecg_amplitudes):.3f} - {max(ecg_amplitudes):.3f} μV\n")
                f.write(f"  平均幅度: {np.mean(ecg_amplitudes):.3f} μV\n")
                f.write(f"  在生理学范围内: {ecg_in_range}/{len(ecg_results)} ({ecg_in_range/len(ecg_results)*100:.1f}%)\n\n")
        
        f.write("详细验证结果:\n")
        f.write("-"*30 + "\n")
        for result in results:
            f.write(f"文件: {result['file_name']}\n")
            if result['success']:
                f.write(f"  EEG幅度: {result['eeg_stats']['amplitude_mean']:.3f} μV\n")
                f.write(f"  EEG在范围内: {result['eeg_in_range']}\n")
                if result['ecg_stats']:
                    f.write(f"  ECG幅度: {result['ecg_stats']['amplitude_mean']:.3f} μV\n")
                    f.write(f"  ECG在范围内: {result['ecg_in_range']}\n")
            else:
                f.write(f"  验证失败: {result['error']}\n")
            f.write("\n")
    
    print(f"验证报告已保存: {report_path}")

def main():
    """主函数"""
    print("开始验证转换后的FIF数据质量")
    print("="*50)
    
    # 抽样验证
    results = sample_verification()
    
    # 分析结果
    analyze_verification_results(results)
    
    # 生成可视化
    create_verification_plot(results)
    
    # 生成报告
    generate_verification_report(results)
    
    print("\n验证完成！")

if __name__ == "__main__":
    main()
