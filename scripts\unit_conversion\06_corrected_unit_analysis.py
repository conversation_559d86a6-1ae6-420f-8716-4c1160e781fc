#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正的单位分析 - 重新评估原始数据和预处理后数据的单位关系

作者：AI助手
日期：2025年1月
"""

import numpy as np
import mne
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def detailed_analysis():
    """详细分析原始数据和预处理后数据"""
    print("详细单位分析")
    print("="*50)
    
    # 分析原始数据
    print("1. 原始数据分析")
    print("-"*30)
    
    original_dir = Path(r"C:\数据\0-5-2矫正正式被试\2-0 eeg set")
    set_file = list(original_dir.glob("*.set"))[0]  # 取第一个文件
    
    print(f"分析文件: {set_file.name}")
    raw_orig = mne.io.read_raw_eeglab(set_file, preload=True, verbose=False)
    data_orig = raw_orig.get_data()
    
    # 原始数据统计
    orig_amplitude = np.mean(np.std(data_orig, axis=1))
    orig_min = np.min(data_orig)
    orig_max = np.max(data_orig)
    orig_range = orig_max - orig_min
    
    print(f"  典型幅度: {orig_amplitude:.6f}")
    print(f"  数据范围: {orig_min:.6f} 到 {orig_max:.6f}")
    print(f"  幅度范围: {orig_range:.6f}")
    
    # 分析预处理后数据
    print("\n2. 预处理后数据分析")
    print("-"*30)
    
    processed_dir = Path(r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突")
    fif_file = list(processed_dir.glob("01_*_rest_*.fif"))[0]  # 取对应的文件
    
    print(f"分析文件: {fif_file.name}")
    raw_proc = mne.io.read_raw_fif(fif_file, preload=True, verbose=False)
    data_proc = raw_proc.get_data()
    
    # 预处理后数据统计
    eeg_data = data_proc[:61, :]
    eeg_amplitude = np.mean(np.std(eeg_data, axis=1))
    eeg_min = np.min(eeg_data)
    eeg_max = np.max(eeg_data)
    eeg_range = eeg_max - eeg_min
    
    print(f"  EEG典型幅度: {eeg_amplitude:.6f} μV")
    print(f"  EEG数据范围: {eeg_min:.6f} 到 {eeg_max:.6f} μV")
    print(f"  EEG幅度范围: {eeg_range:.6f} μV")
    
    # 关键分析：单位推断
    print("\n3. 单位推断分析")
    print("-"*30)
    
    # 计算比例关系
    ratio = eeg_amplitude / orig_amplitude
    print(f"预处理后/原始 = {ratio:.2f}")
    
    # 重新分析原始数据的可能单位
    print(f"\n原始数据幅度: {orig_amplitude:.6f}")
    print("可能的单位判断:")
    
    if orig_amplitude > 1e-3:  # > 1mV
        print("  - 如果是V单位: 转换为μV需要 ×1,000,000")
        expected_uv_from_v = orig_amplitude * 1e6
        print(f"    预期μV幅度: {expected_uv_from_v:.2f}")
        
    if orig_amplitude > 1e-6:  # > 1μV，可能是mV
        print("  - 如果是mV单位: 转换为μV需要 ×1,000")
        expected_uv_from_mv = orig_amplitude * 1e3
        print(f"    预期μV幅度: {expected_uv_from_mv:.2f}")
        
    print("  - 如果已经是μV单位: 不需要转换")
    print(f"    预期μV幅度: {orig_amplitude:.6f}")
    
    # 对比实际结果
    print(f"\n实际预处理后μV幅度: {eeg_amplitude:.6f}")
    
    # 判断最可能的原始单位
    print("\n4. 结论分析")
    print("-"*30)
    
    # 计算各种假设下的误差
    error_if_v = abs(expected_uv_from_v - eeg_amplitude) if 'expected_uv_from_v' in locals() else float('inf')
    error_if_mv = abs(expected_uv_from_mv - eeg_amplitude) if 'expected_uv_from_mv' in locals() else float('inf')
    error_if_uv = abs(orig_amplitude - eeg_amplitude)
    
    print(f"如果原始是V单位，误差: {error_if_v:.6f}")
    print(f"如果原始是mV单位，误差: {error_if_mv:.6f}")
    print(f"如果原始是μV单位，误差: {error_if_uv:.6f}")
    
    # 找出最小误差
    errors = [
        (error_if_v, "V (伏特)"),
        (error_if_mv, "mV (毫伏)"),
        (error_if_uv, "μV (微伏)")
    ]
    
    min_error, best_unit = min(errors, key=lambda x: x[0])
    
    print(f"\n最可能的原始单位: {best_unit}")
    print(f"对应误差: {min_error:.6f}")
    
    # 分析预处理影响
    if best_unit == "μV (微伏)":
        print("\n⚠️  重要发现:")
        print("- 原始数据很可能已经是μV单位")
        print("- 之前的mV→μV转换是错误的")
        print("- 实际上应该不进行单位转换")
        print("- 预处理过程中信号幅度略有增加，可能是由于:")
        print("  * 滤波效果")
        print("  * 重参考操作")
        print("  * 插值处理")
    elif best_unit == "mV (毫伏)":
        print("\n✅ 转换基本正确:")
        print("- 原始数据确实是mV单位")
        print("- mV→μV转换是合理的")
        print("- 但预处理过程对信号幅度有影响")
    
    # 生成最终报告
    generate_final_report(orig_amplitude, eeg_amplitude, best_unit, min_error, ratio)

def generate_final_report(orig_amplitude, proc_amplitude, best_unit, error, ratio):
    """生成最终分析报告"""
    output_dir = Path("result/unit_conversion")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_path = output_dir / f"FINAL_UNIT_ANALYSIS_{timestamp}.txt"
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("最终单位分析报告\n")
        f.write("="*50 + "\n\n")
        f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("关键发现:\n")
        f.write("-"*20 + "\n")
        f.write(f"原始数据典型幅度: {orig_amplitude:.6f}\n")
        f.write(f"预处理后EEG幅度: {proc_amplitude:.6f} μV\n")
        f.write(f"幅度比例: {ratio:.2f}\n")
        f.write(f"最可能的原始单位: {best_unit}\n")
        f.write(f"预测误差: {error:.6f}\n\n")
        
        if best_unit == "μV (微伏)":
            f.write("结论: 单位转换错误\n")
            f.write("-"*20 + "\n")
            f.write("1. 原始数据很可能已经是μV单位\n")
            f.write("2. 之前进行的mV→μV转换(×1000)是不必要的\n")
            f.write("3. 这解释了为什么转换后数据异常偏小\n")
            f.write("4. 实际上应该保持原始单位不变\n\n")
            
            f.write("建议措施:\n")
            f.write("-"*20 + "\n")
            f.write("1. 重新处理数据，不进行单位转换\n")
            f.write("2. 或者将当前数据除以1000恢复到正确幅度\n")
            f.write("3. 验证原始采集设备的单位设置\n")
            f.write("4. 检查EEGLAB文件的单位信息\n")
            
        elif best_unit == "mV (毫伏)":
            f.write("结论: 单位转换基本正确\n")
            f.write("-"*20 + "\n")
            f.write("1. 原始数据确实是mV单位\n")
            f.write("2. mV→μV转换(×1000)是合理的\n")
            f.write("3. 预处理过程对信号幅度有一定影响\n")
            f.write("4. 最终幅度在可接受范围内\n\n")
            
            f.write("建议措施:\n")
            f.write("-"*20 + "\n")
            f.write("1. 当前转换是正确的，可以继续使用\n")
            f.write("2. 检查预处理参数是否过于严格\n")
            f.write("3. 考虑适当调整滤波和重参考参数\n")
        
        f.write(f"\n报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    print(f"\n最终分析报告已保存: {report_path}")

def main():
    """主函数"""
    print("开始修正的单位分析")
    print("="*60)
    detailed_analysis()

if __name__ == "__main__":
    main()
