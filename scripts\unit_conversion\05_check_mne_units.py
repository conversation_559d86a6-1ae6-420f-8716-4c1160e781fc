#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查MNE文件中的实际单位信息
深入分析FIF文件的单位元数据

作者：AI助手
日期：2025年1月
"""

import os
import numpy as np
import mne
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def check_fif_units_metadata():
    """检查FIF文件中的单位元数据"""
    
    print("检查FIF文件中的单位元数据")
    print("="*50)
    
    data_dir = Path(r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突")
    fif_files = list(data_dir.glob("*.fif"))
    
    if not fif_files:
        print("未找到FIF文件！")
        return
    
    sample_file = fif_files[0]
    print(f"分析文件: {sample_file.name}")
    
    # 读取数据
    raw = mne.io.read_raw_fif(sample_file, preload=True, verbose=False)
    
    print(f"\n=== MNE Raw对象信息 ===")
    print(f"通道数: {len(raw.ch_names)}")
    print(f"采样率: {raw.info['sfreq']} Hz")
    print(f"数据长度: {raw.n_times} 采样点")
    
    # 检查通道信息
    print(f"\n=== 通道详细信息 ===")
    for i in range(min(5, len(raw.info['chs']))):
        ch = raw.info['chs'][i]
        print(f"\n通道 {i}: {ch['ch_name']}")
        print(f"  种类: {ch.get('kind', 'Unknown')}")
        print(f"  单位: {ch.get('unit', 'Unknown')}")
        print(f"  单位乘数: {ch.get('unit_mul', 'Unknown')}")
        print(f"  校准: {ch.get('cal', 'Unknown')}")
        print(f"  范围: {ch.get('range', 'Unknown')}")
        print(f"  坐标: {ch.get('loc', 'Unknown')[:3] if ch.get('loc') is not None else 'Unknown'}")
    
    # 检查ECG通道
    print(f"\n=== ECG通道信息 ===")
    for i in range(61, min(64, len(raw.info['chs']))):
        ch = raw.info['chs'][i]
        print(f"\n通道 {i}: {ch['ch_name']}")
        print(f"  种类: {ch.get('kind', 'Unknown')}")
        print(f"  单位: {ch.get('unit', 'Unknown')}")
        print(f"  单位乘数: {ch.get('unit_mul', 'Unknown')}")
        print(f"  校准: {ch.get('cal', 'Unknown')}")
    
    # 解析单位信息
    print(f"\n=== 单位解析 ===")
    
    # MNE单位常量
    try:
        from mne.io.constants import FIFF
        print(f"MNE单位常量:")
        print(f"  FIFF_UNIT_V (伏特): {FIFF.FIFF_UNIT_V}")
        print(f"  FIFF_UNIT_T (特斯拉): {FIFF.FIFF_UNIT_T}")
        print(f"  FIFF_UNITM_NONE: {FIFF.FIFF_UNITM_NONE}")
        
        # 检查实际单位值
        eeg_unit = raw.info['chs'][0].get('unit', None)
        ecg_unit = raw.info['chs'][61].get('unit', None)
        
        print(f"\n实际单位值:")
        print(f"  EEG通道单位值: {eeg_unit}")
        print(f"  ECG通道单位值: {ecg_unit}")
        
        # 单位解释
        if eeg_unit == FIFF.FIFF_UNIT_V:
            print(f"  EEG单位: 伏特 (V)")
        elif eeg_unit is not None:
            print(f"  EEG单位: 未知单位代码 {eeg_unit}")
        
        if ecg_unit == FIFF.FIFF_UNIT_V:
            print(f"  ECG单位: 伏特 (V)")
        elif ecg_unit is not None:
            print(f"  ECG单位: 未知单位代码 {ecg_unit}")
            
    except Exception as e:
        print(f"无法访问MNE单位常量: {e}")
    
    # 检查单位乘数
    print(f"\n=== 单位乘数分析 ===")
    eeg_unit_mul = raw.info['chs'][0].get('unit_mul', None)
    ecg_unit_mul = raw.info['chs'][61].get('unit_mul', None)
    
    print(f"EEG单位乘数: {eeg_unit_mul}")
    print(f"ECG单位乘数: {ecg_unit_mul}")
    
    if eeg_unit_mul is not None:
        print(f"EEG实际单位: {eeg_unit_mul} * 基础单位")
        if eeg_unit_mul == 1e-6:
            print(f"  -> 微伏 (μV)")
        elif eeg_unit_mul == 1e-3:
            print(f"  -> 毫伏 (mV)")
        elif eeg_unit_mul == 1:
            print(f"  -> 伏特 (V)")
    
    if ecg_unit_mul is not None:
        print(f"ECG实际单位: {ecg_unit_mul} * 基础单位")
        if ecg_unit_mul == 1e-6:
            print(f"  -> 微伏 (μV)")
        elif ecg_unit_mul == 1e-3:
            print(f"  -> 毫伏 (mV)")
        elif ecg_unit_mul == 1:
            print(f"  -> 伏特 (V)")
    
    # 数据范围分析
    print(f"\n=== 数据范围与单位一致性检查 ===")
    data = raw.get_data()
    eeg_data = data[:61, :]
    ecg_data = data[61:, :]
    
    eeg_std = np.mean(np.std(eeg_data, axis=1))
    ecg_std = np.mean(np.std(ecg_data, axis=1))
    
    print(f"EEG典型标准差: {eeg_std:.6e}")
    print(f"ECG典型标准差: {ecg_std:.6e}")
    
    # 根据单位乘数推断真实幅度
    if eeg_unit_mul is not None:
        eeg_real_amplitude = eeg_std / eeg_unit_mul
        print(f"EEG推断真实幅度: {eeg_real_amplitude:.2f} 基础单位")
        
        if eeg_unit == 107:  # FIFF_UNIT_V
            print(f"  -> {eeg_real_amplitude:.2f} V = {eeg_real_amplitude*1e6:.2f} μV")
    
    if ecg_unit_mul is not None:
        ecg_real_amplitude = ecg_std / ecg_unit_mul
        print(f"ECG推断真实幅度: {ecg_real_amplitude:.2f} 基础单位")
        
        if ecg_unit == 107:  # FIFF_UNIT_V
            print(f"  -> {ecg_real_amplitude:.2f} V = {ecg_real_amplitude*1e6:.2f} μV")
    
    return {
        'eeg_unit': eeg_unit,
        'ecg_unit': ecg_unit,
        'eeg_unit_mul': eeg_unit_mul,
        'ecg_unit_mul': ecg_unit_mul,
        'eeg_std': eeg_std,
        'ecg_std': ecg_std
    }

def analyze_unit_consistency():
    """分析单位一致性"""
    print(f"\n=== 单位一致性分析 ===")
    
    # 检查多个文件的单位信息
    data_dir = Path(r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突")
    fif_files = list(data_dir.glob("*.fif"))[:3]  # 检查前3个文件
    
    for i, fif_file in enumerate(fif_files):
        print(f"\n文件 {i+1}: {fif_file.name}")
        try:
            raw = mne.io.read_raw_fif(fif_file, preload=False, verbose=False)
            
            eeg_unit = raw.info['chs'][0].get('unit', None)
            eeg_unit_mul = raw.info['chs'][0].get('unit_mul', None)
            ecg_unit = raw.info['chs'][61].get('unit', None)
            ecg_unit_mul = raw.info['chs'][61].get('unit_mul', None)
            
            print(f"  EEG: unit={eeg_unit}, unit_mul={eeg_unit_mul}")
            print(f"  ECG: unit={ecg_unit}, unit_mul={ecg_unit_mul}")
            
        except Exception as e:
            print(f"  读取失败: {e}")

def final_unit_determination():
    """最终单位确定"""
    print(f"\n=== 最终单位确定 ===")
    
    print(f"基于以上分析，最终判断:")
    print(f"1. MNE文件中的单位信息")
    print(f"2. 数据的实际数值范围")
    print(f"3. 预处理步骤的影响")
    print(f"4. 生理学合理性")
    
    print(f"\n结论:")
    print(f"- 如果unit_mul = 1e-6，数据已经是微伏(μV)")
    print(f"- 如果unit_mul = 1e-3，数据是毫伏(mV)")
    print(f"- 如果unit_mul = 1，数据是伏特(V)")
    print(f"- 需要根据unit_mul来确定是否需要转换")

def main():
    """主函数"""
    print("深入检查FIF文件的单位信息")
    print("="*60)
    
    # 检查单位元数据
    unit_info = check_fif_units_metadata()
    
    # 分析单位一致性
    analyze_unit_consistency()
    
    # 最终单位确定
    final_unit_determination()
    
    # 生成最终结论
    print(f"\n" + "="*60)
    print(f"最终结论:")
    
    if unit_info['eeg_unit_mul'] == 1e-6:
        print(f"✅ 数据已经是微伏(μV)单位，无需转换")
        print(f"   EEG幅度 {unit_info['eeg_std']*1e6:.2f} μV 是合理的")
        print(f"   ECG幅度 {unit_info['ecg_std']*1e6:.2f} μV 在重参考后是合理的")
    elif unit_info['eeg_unit_mul'] == 1e-3:
        print(f"⚠️ 数据是毫伏(mV)单位，需要转换为μV")
        print(f"   转换后EEG幅度: {unit_info['eeg_std']*1e3:.2f} μV")
        print(f"   转换后ECG幅度: {unit_info['ecg_std']*1e3:.2f} μV")
    else:
        print(f"❓ 单位信息不明确，需要进一步分析")

if __name__ == "__main__":
    main()
