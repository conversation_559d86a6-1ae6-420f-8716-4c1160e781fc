#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FIF数据单位转换和质量检查脚本
处理位于 D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突 目录中的fif数据文件

功能：
1. 数据读取与观察
2. 单位转换验证
3. 数据质量检查
4. 保存处理

作者：AI助手
日期：2025年1月
"""

import os
import sys
import numpy as np
import pandas as pd
import mne
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class FIFUnitConverter:
    """FIF文件单位转换器"""

    def __init__(self, data_dir):
        """
        初始化转换器

        Parameters:
        -----------
        data_dir : str
            数据目录路径
        """
        self.data_dir = Path(data_dir)
        self.results = []
        self.conversion_log = []

        # 生理学合理范围 (μV)
        self.eeg_range = (1, 100)      # EEG信号：1-100μV
        self.ecg_range = (100, 5000)   # ECG信号：100-5000μV

        print(f"初始化FIF单位转换器")
        print(f"数据目录: {self.data_dir}")
        print(f"EEG生理学范围: {self.eeg_range[0]}-{self.eeg_range[1]} μV")
        print(f"ECG生理学范围: {self.ecg_range[0]}-{self.ecg_range[1]} μV")

    def get_fif_files(self):
        """获取目录中的所有fif文件"""
        fif_files = list(self.data_dir.glob("*.fif"))
        print(f"\n找到 {len(fif_files)} 个fif文件")
        return sorted(fif_files)

    def analyze_single_file(self, fif_path):
        """
        分析单个fif文件

        Parameters:
        -----------
        fif_path : Path
            fif文件路径

        Returns:
        --------
        dict : 分析结果
        """
        print(f"\n正在分析文件: {fif_path.name}")

        try:
            # 读取数据
            raw = mne.io.read_raw_fif(fif_path, preload=True, verbose=False)

            # 获取基本信息
            n_channels = len(raw.ch_names)
            n_samples = raw.n_times
            sfreq = raw.info['sfreq']
            duration = n_samples / sfreq

            print(f"  通道数: {n_channels}")
            print(f"  采样点数: {n_samples}")
            print(f"  采样率: {sfreq} Hz")
            print(f"  时长: {duration:.2f} 秒")

            # 分析通道类型
            eeg_channels = raw.ch_names[:61]  # 前61个通道为EEG
            ecg_channels = raw.ch_names[61:]  # 后面的通道为ECG

            print(f"  EEG通道数: {len(eeg_channels)}")
            print(f"  ECG通道数: {len(ecg_channels)}")

            # 获取数据
            data = raw.get_data()

            # 分析当前单位
            current_unit = self.detect_current_unit(data, eeg_channels, ecg_channels)

            # 计算统计信息
            eeg_data = data[:61, :]
            ecg_data = data[61:, :] if len(ecg_channels) > 0 else None

            eeg_stats = self.calculate_stats(eeg_data, "EEG", current_unit)
            ecg_stats = self.calculate_stats(ecg_data, "ECG", current_unit) if ecg_data is not None else None

            result = {
                'file_name': fif_path.name,
                'file_path': str(fif_path),
                'n_channels': n_channels,
                'n_eeg_channels': len(eeg_channels),
                'n_ecg_channels': len(ecg_channels),
                'n_samples': n_samples,
                'sfreq': sfreq,
                'duration': duration,
                'current_unit': current_unit,
                'eeg_stats': eeg_stats,
                'ecg_stats': ecg_stats,
                'eeg_channels': eeg_channels,
                'ecg_channels': ecg_channels
            }

            return result

        except Exception as e:
            print(f"  错误: {str(e)}")
            return None

    def detect_current_unit(self, data, eeg_channels, ecg_channels):
        """
        检测当前数据的电压单位

        Parameters:
        -----------
        data : ndarray
            数据矩阵
        eeg_channels : list
            EEG通道名称列表
        ecg_channels : list
            ECG通道名称列表

        Returns:
        --------
        str : 检测到的单位
        """
        # 计算数据的典型幅度范围
        eeg_data = data[:61, :]
        eeg_amplitude = np.std(eeg_data, axis=1).mean()

        print(f"  EEG典型幅度: {eeg_amplitude:.6f}")

        # 根据幅度范围判断单位
        if eeg_amplitude > 1e-3:  # > 1mV，可能是V单位
            detected_unit = "V"
        elif eeg_amplitude > 1e-6:  # > 1μV，可能是mV单位
            detected_unit = "mV"
        else:  # 可能已经是μV单位
            detected_unit = "μV"

        print(f"  检测到的单位: {detected_unit}")
        return detected_unit

    def calculate_stats(self, data, signal_type, current_unit):
        """
        计算信号统计信息

        Parameters:
        -----------
        data : ndarray
            信号数据
        signal_type : str
            信号类型 ("EEG" 或 "ECG")
        current_unit : str
            当前单位

        Returns:
        --------
        dict : 统计信息
        """
        if data is None:
            return None

        stats = {
            'mean': np.mean(data),
            'std': np.std(data),
            'min': np.min(data),
            'max': np.max(data),
            'amplitude_range': np.max(data) - np.min(data),
            'channel_std_mean': np.mean(np.std(data, axis=1)),
            'channel_std_std': np.std(np.std(data, axis=1))
        }

        print(f"  {signal_type} 统计信息 ({current_unit}):")
        print(f"    均值: {stats['mean']:.6f}")
        print(f"    标准差: {stats['std']:.6f}")
        print(f"    最小值: {stats['min']:.6f}")
        print(f"    最大值: {stats['max']:.6f}")
        print(f"    幅度范围: {stats['amplitude_range']:.6f}")
        print(f"    通道标准差均值: {stats['channel_std_mean']:.6f}")

        return stats

    def convert_to_microvolts(self, fif_path, current_unit):
        """
        将数据转换为微伏单位

        Parameters:
        -----------
        fif_path : Path
            fif文件路径
        current_unit : str
            当前单位

        Returns:
        --------
        bool : 转换是否成功
        """
        try:
            print(f"\n开始转换文件: {fif_path.name}")
            print(f"当前单位: {current_unit} -> 目标单位: μV")

            # 读取数据
            raw = mne.io.read_raw_fif(fif_path, preload=True, verbose=False)

            # 确定转换因子
            if current_unit == "V":
                conversion_factor = 1e6  # V -> μV
            elif current_unit == "mV":
                conversion_factor = 1e3  # mV -> μV
            else:  # 已经是μV
                conversion_factor = 1.0

            print(f"转换因子: {conversion_factor}")

            # 应用转换
            if conversion_factor != 1.0:
                raw._data *= conversion_factor
                print(f"数据已乘以转换因子 {conversion_factor}")
            else:
                print("数据已经是μV单位，无需转换")

            # 验证转换后的数据
            converted_stats = self.verify_converted_data(raw)

            # 保存转换后的数据
            output_path = fif_path  # 覆盖原文件
            raw.save(output_path, overwrite=True, verbose=False)
            print(f"转换后的数据已保存到: {output_path}")

            # 记录转换日志
            log_entry = {
                'file_name': fif_path.name,
                'original_unit': current_unit,
                'conversion_factor': conversion_factor,
                'converted_stats': converted_stats,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            self.conversion_log.append(log_entry)

            return True

        except Exception as e:
            print(f"转换失败: {str(e)}")
            return False

    def verify_converted_data(self, raw):
        """
        验证转换后的数据质量

        Parameters:
        -----------
        raw : mne.Raw
            转换后的数据

        Returns:
        --------
        dict : 验证结果
        """
        data = raw.get_data()

        # 分离EEG和ECG数据
        eeg_data = data[:61, :]
        ecg_data = data[61:, :] if data.shape[0] > 61 else None

        # 计算转换后的统计信息
        eeg_amplitude = np.std(eeg_data, axis=1).mean()
        eeg_range = (np.min(eeg_data), np.max(eeg_data))

        verification = {
            'eeg_amplitude_mean': eeg_amplitude,
            'eeg_range': eeg_range,
            'eeg_in_physiological_range': self.eeg_range[0] <= eeg_amplitude <= self.eeg_range[1]
        }

        if ecg_data is not None:
            ecg_amplitude = np.std(ecg_data, axis=1).mean()
            ecg_range = (np.min(ecg_data), np.max(ecg_data))
            verification.update({
                'ecg_amplitude_mean': ecg_amplitude,
                'ecg_range': ecg_range,
                'ecg_in_physiological_range': self.ecg_range[0] <= ecg_amplitude <= self.ecg_range[1]
            })

        print(f"验证结果:")
        print(f"  EEG幅度均值: {eeg_amplitude:.2f} μV")
        print(f"  EEG范围: {eeg_range[0]:.2f} 到 {eeg_range[1]:.2f} μV")
        print(f"  EEG在生理学范围内: {verification['eeg_in_physiological_range']}")

        if ecg_data is not None:
            print(f"  ECG幅度均值: {verification['ecg_amplitude_mean']:.2f} μV")
            print(f"  ECG范围: {verification['ecg_range'][0]:.2f} 到 {verification['ecg_range'][1]:.2f} μV")
            print(f"  ECG在生理学范围内: {verification['ecg_in_physiological_range']}")

        return verification

    def process_all_files(self):
        """处理所有文件"""
        print("="*60)
        print("开始处理所有FIF文件")
        print("="*60)

        fif_files = self.get_fif_files()

        if not fif_files:
            print("未找到任何fif文件！")
            return

        # 第一步：分析所有文件
        print("\n第一步：数据读取与观察")
        print("-"*40)

        for fif_path in fif_files:
            result = self.analyze_single_file(fif_path)
            if result:
                self.results.append(result)

        # 第二步：单位转换
        print("\n第二步：单位转换验证")
        print("-"*40)

        for result in self.results:
            fif_path = Path(result['file_path'])
            current_unit = result['current_unit']

            success = self.convert_to_microvolts(fif_path, current_unit)
            result['conversion_success'] = success

        # 第三步：生成报告
        print("\n第三步：生成处理报告")
        print("-"*40)

        self.generate_report()

        print("\n处理完成！")

    def generate_report(self):
        """生成处理报告"""
        print("正在生成处理报告...")

        # 创建输出目录
        output_dir = Path("result/unit_conversion")
        output_dir.mkdir(parents=True, exist_ok=True)

        # 生成汇总报告
        self.generate_summary_report(output_dir)

        # 生成详细统计报告
        self.generate_detailed_report(output_dir)

        # 生成可视化报告
        self.generate_visualization_report(output_dir)

        print(f"报告已保存到: {output_dir}")

    def generate_summary_report(self, output_dir):
        """生成汇总报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = output_dir / f"unit_conversion_summary_{timestamp}.txt"

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("FIF数据单位转换处理报告\n")
            f.write("="*50 + "\n\n")
            f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"数据目录: {self.data_dir}\n")
            f.write(f"处理文件数: {len(self.results)}\n\n")

            # 汇总统计
            successful_conversions = sum(1 for r in self.results if r.get('conversion_success', False))
            f.write(f"转换成功: {successful_conversions} 个文件\n")
            f.write(f"转换失败: {len(self.results) - successful_conversions} 个文件\n\n")

            # 单位分布统计
            unit_counts = {}
            for result in self.results:
                unit = result['current_unit']
                unit_counts[unit] = unit_counts.get(unit, 0) + 1

            f.write("原始单位分布:\n")
            for unit, count in unit_counts.items():
                f.write(f"  {unit}: {count} 个文件\n")
            f.write("\n")

            # 数据质量检查结果
            f.write("数据质量检查结果:\n")
            eeg_in_range = 0
            ecg_in_range = 0
            total_with_ecg = 0

            for log_entry in self.conversion_log:
                if log_entry['converted_stats']['eeg_in_physiological_range']:
                    eeg_in_range += 1
                if 'ecg_in_physiological_range' in log_entry['converted_stats']:
                    total_with_ecg += 1
                    if log_entry['converted_stats']['ecg_in_physiological_range']:
                        ecg_in_range += 1

            f.write(f"  EEG信号在生理学范围内: {eeg_in_range}/{len(self.results)} 个文件\n")
            if total_with_ecg > 0:
                f.write(f"  ECG信号在生理学范围内: {ecg_in_range}/{total_with_ecg} 个文件\n")
            f.write("\n")

            # 详细文件列表
            f.write("详细文件处理结果:\n")
            f.write("-"*50 + "\n")
            for result in self.results:
                f.write(f"文件: {result['file_name']}\n")
                f.write(f"  通道数: {result['n_channels']} (EEG: {result['n_eeg_channels']}, ECG: {result['n_ecg_channels']})\n")
                f.write(f"  原始单位: {result['current_unit']}\n")
                f.write(f"  转换状态: {'成功' if result.get('conversion_success', False) else '失败'}\n")
                f.write(f"  时长: {result['duration']:.2f} 秒\n")
                f.write("\n")

        print(f"汇总报告已保存: {report_path}")

    def generate_detailed_report(self, output_dir):
        """生成详细统计报告"""
        if not self.results:
            return

        # 创建DataFrame
        df_data = []
        for result in self.results:
            row = {
                '文件名': result['file_name'],
                '总通道数': result['n_channels'],
                'EEG通道数': result['n_eeg_channels'],
                'ECG通道数': result['n_ecg_channels'],
                '采样点数': result['n_samples'],
                '采样率(Hz)': result['sfreq'],
                '时长(秒)': result['duration'],
                '原始单位': result['current_unit'],
                '转换状态': '成功' if result.get('conversion_success', False) else '失败'
            }

            # 添加EEG统计信息
            if result['eeg_stats']:
                row.update({
                    'EEG均值': result['eeg_stats']['mean'],
                    'EEG标准差': result['eeg_stats']['std'],
                    'EEG最小值': result['eeg_stats']['min'],
                    'EEG最大值': result['eeg_stats']['max'],
                    'EEG幅度范围': result['eeg_stats']['amplitude_range']
                })

            # 添加ECG统计信息
            if result['ecg_stats']:
                row.update({
                    'ECG均值': result['ecg_stats']['mean'],
                    'ECG标准差': result['ecg_stats']['std'],
                    'ECG最小值': result['ecg_stats']['min'],
                    'ECG最大值': result['ecg_stats']['max'],
                    'ECG幅度范围': result['ecg_stats']['amplitude_range']
                })

            df_data.append(row)

        df = pd.DataFrame(df_data)

        # 保存详细报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        excel_path = output_dir / f"unit_conversion_detailed_{timestamp}.xlsx"
        df.to_excel(excel_path, index=False)
        print(f"详细报告已保存: {excel_path}")

    def generate_visualization_report(self, output_dir):
        """生成可视化报告"""
        if not self.results:
            return

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 创建图形
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('FIF数据单位转换分析报告', fontsize=16, fontweight='bold')

        # 1. 单位分布饼图
        unit_counts = {}
        for result in self.results:
            unit = result['current_unit']
            unit_counts[unit] = unit_counts.get(unit, 0) + 1

        axes[0, 0].pie(unit_counts.values(), labels=unit_counts.keys(), autopct='%1.1f%%')
        axes[0, 0].set_title('原始数据单位分布')

        # 2. 转换成功率
        success_count = sum(1 for r in self.results if r.get('conversion_success', False))
        fail_count = len(self.results) - success_count

        axes[0, 1].pie([success_count, fail_count], labels=['成功', '失败'],
                      colors=['green', 'red'], autopct='%1.1f%%')
        axes[0, 1].set_title('单位转换成功率')

        # 3. 文件大小分布（基于时长）
        durations = [result['duration'] for result in self.results]
        axes[1, 0].hist(durations, bins=20, alpha=0.7, color='skyblue')
        axes[1, 0].set_xlabel('时长 (秒)')
        axes[1, 0].set_ylabel('文件数量')
        axes[1, 0].set_title('文件时长分布')

        # 4. 通道数分布
        channel_counts = [result['n_channels'] for result in self.results]
        axes[1, 1].hist(channel_counts, bins=20, alpha=0.7, color='lightcoral')
        axes[1, 1].set_xlabel('通道数')
        axes[1, 1].set_ylabel('文件数量')
        axes[1, 1].set_title('通道数分布')

        plt.tight_layout()

        # 保存图形
        plot_path = output_dir / f"unit_conversion_visualization_{timestamp}.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"可视化报告已保存: {plot_path}")

def main():
    """主函数"""
    # 数据目录
    data_dir = r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突"

    # 检查目录是否存在
    if not os.path.exists(data_dir):
        print(f"错误：数据目录不存在: {data_dir}")
        return

    # 创建转换器并处理
    converter = FIFUnitConverter(data_dir)
    converter.process_all_files()

if __name__ == "__main__":
    main()
