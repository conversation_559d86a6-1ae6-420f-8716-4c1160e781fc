#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的原始数据与预处理后数据幅度对比分析

作者：AI助手
日期：2025年1月
"""

import numpy as np
import mne
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def analyze_original_data():
    """分析原始数据"""
    print("分析原始数据")
    print("="*40)
    
    original_dir = Path(r"C:\数据\0-5-2矫正正式被试\2-0 eeg set")
    
    # 选择前3个.set文件进行分析
    set_files = list(original_dir.glob("*.set"))[:3]
    
    original_results = []
    
    for set_file in set_files:
        try:
            print(f"\n分析文件: {set_file.name}")
            
            # 读取EEGLAB文件
            raw = mne.io.read_raw_eeglab(set_file, preload=True, verbose=False)
            data = raw.get_data()
            
            # 计算整体幅度统计
            overall_amplitude = np.mean(np.std(data, axis=1))
            data_range = np.max(data) - np.min(data)
            
            print(f"  通道数: {data.shape[0]}")
            print(f"  采样点数: {data.shape[1]}")
            print(f"  数据范围: {np.min(data):.6f} 到 {np.max(data):.6f}")
            print(f"  典型幅度(标准差均值): {overall_amplitude:.6f}")
            print(f"  数据幅度范围: {data_range:.6f}")
            
            # 判断单位
            if overall_amplitude > 1e-3:
                unit = "V (伏特)"
            elif overall_amplitude > 1e-6:
                unit = "mV (毫伏)"
            else:
                unit = "μV (微伏)"
            
            print(f"  推测单位: {unit}")
            
            original_results.append({
                'file_name': set_file.name,
                'amplitude': overall_amplitude,
                'unit': unit,
                'data_range': data_range,
                'n_channels': data.shape[0]
            })
            
        except Exception as e:
            print(f"  分析失败: {str(e)}")
    
    return original_results

def analyze_processed_data():
    """分析预处理后数据"""
    print("\n\n分析预处理后数据")
    print("="*40)
    
    processed_dir = Path(r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突")
    
    # 选择前3个.fif文件进行分析
    fif_files = list(processed_dir.glob("01_*_rest_*.fif"))[:3]
    
    processed_results = []
    
    for fif_file in fif_files:
        try:
            print(f"\n分析文件: {fif_file.name}")
            
            # 读取FIF文件
            raw = mne.io.read_raw_fif(fif_file, preload=True, verbose=False)
            data = raw.get_data()
            
            # 分离EEG和ECG数据
            eeg_data = data[:61, :]
            ecg_data = data[61:, :] if data.shape[0] > 61 else None
            
            # 计算幅度
            eeg_amplitude = np.mean(np.std(eeg_data, axis=1))
            ecg_amplitude = np.mean(np.std(ecg_data, axis=1)) if ecg_data is not None else None
            
            print(f"  总通道数: {data.shape[0]}")
            print(f"  EEG通道数: 61")
            print(f"  ECG通道数: {data.shape[0] - 61}")
            print(f"  EEG典型幅度: {eeg_amplitude:.6f} μV")
            if ecg_amplitude:
                print(f"  ECG典型幅度: {ecg_amplitude:.6f} μV")
            
            processed_results.append({
                'file_name': fif_file.name,
                'eeg_amplitude': eeg_amplitude,
                'ecg_amplitude': ecg_amplitude,
                'n_channels': data.shape[0]
            })
            
        except Exception as e:
            print(f"  分析失败: {str(e)}")
    
    return processed_results

def generate_comparison_analysis(original_results, processed_results):
    """生成对比分析"""
    print("\n\n对比分析结果")
    print("="*50)
    
    if not original_results or not processed_results:
        print("缺少数据，无法进行对比分析")
        return
    
    # 取第一个文件进行详细对比
    orig = original_results[0]
    proc = processed_results[0]
    
    print(f"原始数据 ({orig['file_name']}):")
    print(f"  典型幅度: {orig['amplitude']:.6f}")
    print(f"  推测单位: {orig['unit']}")
    print(f"  数据范围: {orig['data_range']:.6f}")
    
    print(f"\n预处理后数据 ({proc['file_name']}):")
    print(f"  EEG幅度: {proc['eeg_amplitude']:.6f} μV")
    if proc['ecg_amplitude']:
        print(f"  ECG幅度: {proc['ecg_amplitude']:.6f} μV")
    
    # 计算转换关系
    if proc['eeg_amplitude'] > 0:
        conversion_ratio = orig['amplitude'] / proc['eeg_amplitude']
        print(f"\n转换关系分析:")
        print(f"  原始/预处理后 = {conversion_ratio:.0f}")
        
        # 分析转换合理性
        print(f"\n合理性分析:")
        if 800 <= conversion_ratio <= 1200:
            print("  ✅ 转换合理: 原始数据确实是mV单位")
            print("  ✅ 1000倍的转换因子是正确的")
            print("  ⚠️  但预处理后幅度仍然偏小，可能是预处理过程中信号被过度衰减")
        elif conversion_ratio > 10000:
            print("  ❌ 转换可能有问题: 原始数据可能是V单位，不是mV")
            print("  💡 建议使用1,000,000倍转换因子 (V → μV)")
        elif conversion_ratio < 100:
            print("  ❌ 转换可能有问题: 原始数据可能已经是μV单位")
            print("  💡 建议不进行单位转换")
        else:
            print("  ⚠️  转换关系异常，需要进一步检查")
    
    # 生成详细报告
    generate_detailed_report(original_results, processed_results, conversion_ratio if 'conversion_ratio' in locals() else None)

def generate_detailed_report(original_results, processed_results, conversion_ratio):
    """生成详细报告文件"""
    output_dir = Path("result/unit_conversion")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_path = output_dir / f"amplitude_comparison_analysis_{timestamp}.txt"
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("原始数据与预处理后数据幅度对比分析报告\n")
        f.write("="*60 + "\n\n")
        f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("一、原始数据分析结果\n")
        f.write("-"*30 + "\n")
        for result in original_results:
            f.write(f"文件: {result['file_name']}\n")
            f.write(f"  通道数: {result['n_channels']}\n")
            f.write(f"  典型幅度: {result['amplitude']:.6f}\n")
            f.write(f"  推测单位: {result['unit']}\n")
            f.write(f"  数据范围: {result['data_range']:.6f}\n\n")
        
        f.write("二、预处理后数据分析结果\n")
        f.write("-"*30 + "\n")
        for result in processed_results:
            f.write(f"文件: {result['file_name']}\n")
            f.write(f"  总通道数: {result['n_channels']}\n")
            f.write(f"  EEG幅度: {result['eeg_amplitude']:.6f} μV\n")
            if result['ecg_amplitude']:
                f.write(f"  ECG幅度: {result['ecg_amplitude']:.6f} μV\n")
            f.write("\n")
        
        f.write("三、对比分析结论\n")
        f.write("-"*30 + "\n")
        
        if conversion_ratio:
            f.write(f"转换比例: {conversion_ratio:.0f}:1\n\n")
            
            if 800 <= conversion_ratio <= 1200:
                f.write("结论: 单位转换基本正确\n")
                f.write("- 原始数据确实是mV单位\n")
                f.write("- 1000倍转换因子是合理的\n")
                f.write("- 但预处理后信号幅度偏小，可能原因:\n")
                f.write("  * 滤波参数过于严格\n")
                f.write("  * 重参考操作降低了信号幅度\n")
                f.write("  * 基线校正过度\n")
                f.write("  * 坏通道插值影响\n\n")
                
                f.write("建议:\n")
                f.write("- 检查滤波参数设置\n")
                f.write("- 验证重参考方法的合理性\n")
                f.write("- 调整预处理参数以保持合理的信号幅度\n")
                
            elif conversion_ratio > 10000:
                f.write("结论: 单位判断可能有误\n")
                f.write("- 原始数据可能是V单位，不是mV\n")
                f.write("- 应该使用1,000,000倍转换因子\n")
                f.write("- 当前转换导致信号过小\n")
                
            else:
                f.write("结论: 转换关系异常，需要进一步检查\n")
        
        f.write(f"\n报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    print(f"\n详细报告已保存: {report_path}")

def main():
    """主函数"""
    print("开始原始数据与预处理后数据的幅度对比分析")
    print("="*60)
    
    # 分析原始数据
    original_results = analyze_original_data()
    
    # 分析预处理后数据
    processed_results = analyze_processed_data()
    
    # 生成对比分析
    generate_comparison_analysis(original_results, processed_results)

if __name__ == "__main__":
    main()
