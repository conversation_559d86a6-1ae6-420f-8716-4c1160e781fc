#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
心电数据单位分析
分析原始心电MAT文件和预处理后FIF文件中ECG通道的单位转换

作者：AI助手
日期：2025年1月
"""

import os
import numpy as np
import scipy.io as sio
import h5py
import mne
from pathlib import Path
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ECGUnitAnalyzer:
    """心电数据单位分析器"""

    def __init__(self):
        self.ecg_original_dir = Path(r"C:\数据\0-5-2矫正正式被试\1-0 bspm mat")
        self.processed_dir = Path(r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突")
        self.results = []

    def analyze_original_ecg_mat(self, mat_path):
        """
        分析原始心电MAT文件

        Parameters:
        -----------
        mat_path : Path
            MAT文件路径

        Returns:
        --------
        dict : 分析结果
        """
        try:
            print(f"分析原始心电文件: {mat_path.name}")

            # 尝试读取MAT文件（先尝试scipy，再尝试h5py）
            mat_data = None
            try:
                mat_data = sio.loadmat(mat_path)
                print("  使用scipy.io读取成功")
            except:
                print("  scipy.io读取失败，尝试h5py读取...")
                with h5py.File(mat_path, 'r') as f:
                    mat_data = {}
                    for key in f.keys():
                        if isinstance(f[key], h5py.Dataset):
                            mat_data[key] = f[key][:]
                print("  使用h5py读取成功")

            # 查看MAT文件结构
            print(f"  MAT文件键: {list(mat_data.keys())}")

            # 寻找数据字段
            data_keys = [k for k in mat_data.keys() if not k.startswith('__')]
            print(f"  数据字段: {data_keys}")

            # 获取主要数据 - 优先选择RawData
            main_data = None
            data_key = None

            # 首先查找RawData字段
            if 'RawData' in mat_data:
                main_data = mat_data['RawData']
                data_key = 'RawData'
            else:
                # 如果没有RawData，寻找其他2D数据
                for key in data_keys:
                    if isinstance(mat_data[key], np.ndarray) and mat_data[key].ndim >= 2:
                        main_data = mat_data[key]
                        data_key = key
                        break

            if main_data is None:
                print("  未找到有效的数据矩阵")
                return {'success': False, 'error': '未找到数据矩阵'}

            print(f"  数据字段名: {data_key}")
            print(f"  数据形状: {main_data.shape}")
            print(f"  数据类型: {main_data.dtype}")

            # 分析数据幅度
            data_stats = self.analyze_ecg_amplitude(main_data)

            # 检测可能的单位
            detected_unit = self.detect_ecg_unit(data_stats['typical_amplitude'])

            result = {
                'file_name': mat_path.name,
                'file_path': str(mat_path),
                'data_key': data_key,
                'data_shape': main_data.shape,
                'data_stats': data_stats,
                'detected_unit': detected_unit,
                'success': True
            }

            return result

        except Exception as e:
            print(f"  分析失败: {str(e)}")
            return {
                'file_name': mat_path.name,
                'success': False,
                'error': str(e)
            }

    def analyze_ecg_amplitude(self, data):
        """
        分析心电数据幅度特征

        Parameters:
        -----------
        data : ndarray
            心电数据矩阵

        Returns:
        --------
        dict : 幅度统计信息
        """
        # 整体统计
        stats = {
            'mean': np.mean(data),
            'std': np.std(data),
            'min': np.min(data),
            'max': np.max(data),
            'amplitude_range': np.max(data) - np.min(data),
            'typical_amplitude': np.mean(np.std(data, axis=1)) if data.ndim > 1 else np.std(data)
        }

        print(f"  心电数据统计:")
        print(f"    均值: {stats['mean']:.6f}")
        print(f"    标准差: {stats['std']:.6f}")
        print(f"    最小值: {stats['min']:.6f}")
        print(f"    最大值: {stats['max']:.6f}")
        print(f"    幅度范围: {stats['amplitude_range']:.6f}")
        print(f"    典型幅度: {stats['typical_amplitude']:.6f}")

        return stats

    def detect_ecg_unit(self, amplitude):
        """
        根据幅度检测心电数据的可能单位

        Parameters:
        -----------
        amplitude : float
            典型幅度值

        Returns:
        --------
        str : 检测到的单位
        """
        # ECG信号的典型幅度范围
        if amplitude > 1:  # > 1V，不太可能
            return "V (伏特) - 异常大"
        elif amplitude > 1e-3:  # > 1mV，正常ECG范围
            return "mV (毫伏)"
        elif amplitude > 1e-6:  # > 1μV，可能是μV
            return "μV (微伏)"
        else:
            return "未知单位 - 异常小"

    def analyze_processed_ecg(self, fif_path):
        """分析预处理后FIF文件中的ECG通道"""
        try:
            print(f"分析预处理后ECG: {fif_path.name}")

            raw = mne.io.read_raw_fif(fif_path, preload=True, verbose=False)
            data = raw.get_data()

            # ECG通道是后58个
            ecg_data = data[61:, :] if data.shape[0] > 61 else None

            if ecg_data is None:
                return {'success': False, 'error': '未找到ECG通道'}

            # 分析ECG数据
            ecg_stats = self.analyze_ecg_amplitude(ecg_data)

            # 检查MNE的单位信息
            ecg_channel_info = []
            for i in range(61, min(61+5, len(raw.ch_names))):  # 检查前5个ECG通道
                ch_info = raw.info['chs'][i]
                unit = ch_info.get('unit', 'unknown')
                unit_mul = ch_info.get('unit_mul', 1)
                ecg_channel_info.append({
                    'name': raw.ch_names[i],
                    'unit': unit,
                    'unit_mul': unit_mul
                })

            print(f"  ECG通道信息:")
            for ch_info in ecg_channel_info:
                print(f"    {ch_info['name']}: unit={ch_info['unit']}, unit_mul={ch_info['unit_mul']}")

            return {
                'file_name': fif_path.name,
                'ecg_stats': ecg_stats,
                'ecg_channel_info': ecg_channel_info,
                'n_ecg_channels': ecg_data.shape[0],
                'success': True
            }

        except Exception as e:
            print(f"  分析失败: {str(e)}")
            return {'success': False, 'error': str(e)}

    def compare_ecg_data(self):
        """对比原始心电数据和预处理后的心电数据"""
        print("心电数据单位转换分析")
        print("="*60)

        # 分析原始心电数据
        print("1. 原始心电数据分析")
        print("-"*40)

        mat_files = list(self.ecg_original_dir.glob("*.mat"))

        # 选择前3个文件进行分析
        sample_mat_files = mat_files[:3]

        original_results = []
        for mat_file in sample_mat_files:
            result = self.analyze_original_ecg_mat(mat_file)
            original_results.append(result)
            print()

        # 分析预处理后的心电数据
        print("2. 预处理后心电数据分析")
        print("-"*40)

        fif_files = list(self.processed_dir.glob("01_*_rest_*.fif"))

        # 选择前3个文件进行分析
        sample_fif_files = fif_files[:3]

        processed_results = []
        for fif_file in sample_fif_files:
            result = self.analyze_processed_ecg(fif_file)
            processed_results.append(result)
            print()

        # 生成对比分析
        self.generate_ecg_comparison_report(original_results, processed_results)

    def generate_ecg_comparison_report(self, original_results, processed_results):
        """生成心电数据对比报告"""
        print("3. 心电数据对比分析")
        print("-"*40)

        # 提取成功的结果
        orig_success = [r for r in original_results if r['success']]
        proc_success = [r for r in processed_results if r['success']]

        if not orig_success or not proc_success:
            print("缺少有效数据，无法进行对比")
            return

        # 取第一个文件进行详细对比
        orig = orig_success[0]
        proc = proc_success[0]

        print(f"原始心电数据 ({orig['file_name']}):")
        print(f"  数据形状: {orig['data_shape']}")
        print(f"  典型幅度: {orig['data_stats']['typical_amplitude']:.6f}")
        print(f"  推测单位: {orig['detected_unit']}")
        print(f"  幅度范围: {orig['data_stats']['amplitude_range']:.6f}")

        print(f"\n预处理后心电数据 ({proc['file_name']}):")
        print(f"  ECG通道数: {proc['n_ecg_channels']}")
        print(f"  典型幅度: {proc['ecg_stats']['typical_amplitude']:.6f}")
        print(f"  幅度范围: {proc['ecg_stats']['amplitude_range']:.6f}")

        # 计算转换关系
        if proc['ecg_stats']['typical_amplitude'] > 0:
            conversion_ratio = orig['data_stats']['typical_amplitude'] / proc['ecg_stats']['typical_amplitude']
            print(f"\n转换关系分析:")
            print(f"  原始/预处理后 = {conversion_ratio:.2f}")

            # 分析转换合理性
            self.analyze_ecg_conversion_validity(orig, proc, conversion_ratio)

        # 生成详细报告文件
        self.generate_ecg_report_file(original_results, processed_results)

    def analyze_ecg_conversion_validity(self, orig, proc, ratio):
        """分析心电数据转换的合理性"""
        print(f"\n心电数据转换合理性分析:")

        orig_amp = orig['data_stats']['typical_amplitude']
        proc_amp = proc['ecg_stats']['typical_amplitude']

        # ECG信号的正常范围判断
        print(f"原始心电幅度: {orig_amp:.6f} ({orig['detected_unit']})")
        print(f"预处理后心电幅度: {proc_amp:.6f}")

        # 根据原始单位判断转换是否合理
        if "mV" in orig['detected_unit']:
            # 如果原始是mV，转换为μV应该是×1000
            expected_uv = orig_amp * 1000
            print(f"如果原始是mV，预期μV幅度: {expected_uv:.2f}")
            print(f"实际预处理后幅度: {proc_amp:.6f}")

            if abs(proc_amp - expected_uv) / expected_uv < 0.5:  # 50%误差范围内
                print("✅ 心电数据转换合理")
                print("- 原始数据确实是mV单位")
                print("- mV→μV转换(×1000)是正确的")
            else:
                print("⚠️ 心电数据转换可能有问题")
                print("- 转换后幅度与预期不符")

        elif "μV" in orig['detected_unit']:
            print("⚠️ 原始心电数据可能已经是μV单位")
            print("- 如果是这样，不应该进行×1000转换")

        # ECG生理范围检查
        ecg_normal_range_uv = (100, 5000)  # μV
        if ecg_normal_range_uv[0] <= proc_amp <= ecg_normal_range_uv[1]:
            print(f"✅ 预处理后ECG幅度在正常生理范围内 ({ecg_normal_range_uv[0]}-{ecg_normal_range_uv[1]} μV)")
        else:
            print(f"⚠️ 预处理后ECG幅度超出正常生理范围 ({ecg_normal_range_uv[0]}-{ecg_normal_range_uv[1]} μV)")

    def generate_ecg_report_file(self, original_results, processed_results):
        """生成心电数据分析报告文件"""
        output_dir = Path("result/unit_conversion")
        output_dir.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_path = output_dir / f"ECG_unit_analysis_{timestamp}.txt"

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("心电数据单位转换分析报告\n")
            f.write("="*60 + "\n\n")
            f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("一、原始心电数据分析\n")
            f.write("-"*30 + "\n")
            for result in original_results:
                if result['success']:
                    f.write(f"文件: {result['file_name']}\n")
                    f.write(f"  数据形状: {result['data_shape']}\n")
                    f.write(f"  典型幅度: {result['data_stats']['typical_amplitude']:.6f}\n")
                    f.write(f"  推测单位: {result['detected_unit']}\n")
                    f.write(f"  幅度范围: {result['data_stats']['amplitude_range']:.6f}\n\n")

            f.write("二、预处理后心电数据分析\n")
            f.write("-"*30 + "\n")
            for result in processed_results:
                if result['success']:
                    f.write(f"文件: {result['file_name']}\n")
                    f.write(f"  ECG通道数: {result['n_ecg_channels']}\n")
                    f.write(f"  典型幅度: {result['ecg_stats']['typical_amplitude']:.6f}\n")
                    f.write(f"  幅度范围: {result['ecg_stats']['amplitude_range']:.6f}\n\n")

            f.write("三、转换合理性结论\n")
            f.write("-"*30 + "\n")
            f.write("详细分析请参考控制台输出\n")

            f.write(f"\n报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

        print(f"\n心电数据分析报告已保存: {report_path}")

def main():
    """主函数"""
    analyzer = ECGUnitAnalyzer()
    analyzer.compare_ecg_data()

if __name__ == "__main__":
    main()
