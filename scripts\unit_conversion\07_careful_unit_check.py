#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仔细检查单位转换 - 验证MNE读取的单位信息

作者：AI助手
日期：2025年1月
"""

import numpy as np
import mne
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def check_mne_units():
    """检查MNE读取的单位信息"""
    print("检查MNE读取的单位信息")
    print("="*50)
    
    # 检查原始数据
    print("1. 原始EEGLAB数据")
    print("-"*30)
    
    original_dir = Path(r"C:\数据\0-5-2矫正正式被试\2-0 eeg set")
    set_file = list(original_dir.glob("*.set"))[0]
    
    print(f"文件: {set_file.name}")
    raw_orig = mne.io.read_raw_eeglab(set_file, preload=True, verbose=False)
    
    # 检查通道信息
    print(f"通道数: {len(raw_orig.ch_names)}")
    print(f"前10个通道名: {raw_orig.ch_names[:10]}")
    
    # 检查单位信息
    print("\n通道单位信息:")
    for i, ch_name in enumerate(raw_orig.ch_names[:5]):
        ch_info = raw_orig.info['chs'][i]
        unit = ch_info.get('unit', 'unknown')
        unit_mul = ch_info.get('unit_mul', 1)
        print(f"  {ch_name}: unit={unit}, unit_mul={unit_mul}")
    
    # 获取数据并检查
    data_orig = raw_orig.get_data()
    print(f"\n数据形状: {data_orig.shape}")
    print(f"数据类型: {data_orig.dtype}")
    print(f"数据范围: {np.min(data_orig):.8f} 到 {np.max(data_orig):.8f}")
    
    # 计算典型幅度
    orig_amplitude = np.mean(np.std(data_orig, axis=1))
    print(f"典型幅度: {orig_amplitude:.8f}")
    
    # 检查预处理后数据
    print("\n2. 预处理后FIF数据")
    print("-"*30)
    
    processed_dir = Path(r"D:\ecgeeg\18-eegecg手动预处理5-重参考2-双侧乳突")
    fif_file = list(processed_dir.glob("01_*_rest_*.fif"))[0]
    
    print(f"文件: {fif_file.name}")
    raw_proc = mne.io.read_raw_fif(fif_file, preload=True, verbose=False)
    
    # 检查通道信息
    print(f"通道数: {len(raw_proc.ch_names)}")
    print(f"前10个通道名: {raw_proc.ch_names[:10]}")
    
    # 检查单位信息
    print("\n通道单位信息:")
    for i, ch_name in enumerate(raw_proc.ch_names[:5]):
        ch_info = raw_proc.info['chs'][i]
        unit = ch_info.get('unit', 'unknown')
        unit_mul = ch_info.get('unit_mul', 1)
        print(f"  {ch_name}: unit={unit}, unit_mul={unit_mul}")
    
    # 获取数据并检查
    data_proc = raw_proc.get_data()
    print(f"\n数据形状: {data_proc.shape}")
    print(f"数据类型: {data_proc.dtype}")
    print(f"数据范围: {np.min(data_proc):.8f} 到 {np.max(data_proc):.8f}")
    
    # 分离EEG数据
    eeg_data = data_proc[:61, :]
    eeg_amplitude = np.mean(np.std(eeg_data, axis=1))
    print(f"EEG典型幅度: {eeg_amplitude:.8f}")
    
    # 详细对比分析
    print("\n3. 详细对比分析")
    print("-"*30)
    
    print(f"原始数据典型幅度: {orig_amplitude:.8f}")
    print(f"预处理后EEG幅度: {eeg_amplitude:.8f}")
    
    # 检查MNE的单位转换
    print("\n4. MNE单位转换检查")
    print("-"*30)
    
    # 检查原始数据的实际单位
    # MNE通常会自动转换到伏特(V)
    print("MNE读取数据时的单位处理:")
    print("- MNE通常将所有数据转换为伏特(V)作为内部单位")
    print("- 如果原始数据是μV，MNE会除以1,000,000")
    print("- 如果原始数据是mV，MNE会除以1,000")
    
    # 重新计算假设
    print("\n5. 重新计算单位假设")
    print("-"*30)
    
    # 假设原始数据在MNE中已经是V单位
    orig_in_uv = orig_amplitude * 1e6  # 转换为μV
    proc_in_uv = eeg_amplitude  # 已经标记为μV
    
    print(f"如果原始数据(MNE读取)是V单位:")
    print(f"  转换为μV: {orig_in_uv:.2f} μV")
    print(f"  预处理后: {proc_in_uv:.6f} μV")
    print(f"  比例: {proc_in_uv / orig_in_uv:.6f}")
    
    # 这个比例应该接近1/1000，因为我们进行了×1000的转换
    expected_ratio = 1/1000
    actual_ratio = proc_in_uv / orig_in_uv
    
    print(f"\n预期比例(如果转换×1000): {expected_ratio:.6f}")
    print(f"实际比例: {actual_ratio:.6f}")
    print(f"比例差异: {abs(actual_ratio - expected_ratio):.6f}")
    
    # 最终判断
    print("\n6. 最终判断")
    print("-"*30)
    
    if abs(actual_ratio - expected_ratio) < 0.001:
        print("✅ 单位转换是正确的")
        print("- 原始数据确实需要×1000转换")
        print("- 当前转换后的数据是正确的")
    else:
        print("❌ 单位转换可能有问题")
        print("- 需要重新检查转换逻辑")
    
    # 生成最终结论报告
    generate_conclusion_report(orig_amplitude, eeg_amplitude, orig_in_uv, actual_ratio, expected_ratio)

def generate_conclusion_report(orig_amp, proc_amp, orig_uv, actual_ratio, expected_ratio):
    """生成结论报告"""
    output_dir = Path("result/unit_conversion")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_path = output_dir / f"UNIT_CONVERSION_CONCLUSION_{timestamp}.txt"
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("单位转换最终结论报告\n")
        f.write("="*50 + "\n\n")
        f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("数据分析结果:\n")
        f.write("-"*20 + "\n")
        f.write(f"原始数据幅度(MNE读取): {orig_amp:.8f} V\n")
        f.write(f"转换为μV: {orig_uv:.2f} μV\n")
        f.write(f"预处理后EEG幅度: {proc_amp:.8f} μV\n")
        f.write(f"实际转换比例: {actual_ratio:.6f}\n")
        f.write(f"预期转换比例: {expected_ratio:.6f}\n")
        f.write(f"比例差异: {abs(actual_ratio - expected_ratio):.6f}\n\n")
        
        if abs(actual_ratio - expected_ratio) < 0.001:
            f.write("结论: 单位转换正确\n")
            f.write("-"*20 + "\n")
            f.write("1. 原始数据确实需要进行单位转换\n")
            f.write("2. ×1000的转换因子是合理的\n")
            f.write("3. 当前预处理后的数据单位是正确的\n")
            f.write("4. 之前认为'异常偏小'的判断是错误的\n\n")
            
            f.write("说明:\n")
            f.write("- MNE读取EEGLAB数据时自动转换为V单位\n")
            f.write("- 我们的×1000转换将其正确转换为μV\n")
            f.write("- 最终数据幅度在合理范围内\n")
            
        else:
            f.write("结论: 单位转换可能有问题\n")
            f.write("-"*20 + "\n")
            f.write("1. 转换比例与预期不符\n")
            f.write("2. 需要重新检查转换逻辑\n")
            f.write("3. 可能需要调整转换因子\n")
        
        f.write(f"\n报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    print(f"\n最终结论报告已保存: {report_path}")

def main():
    """主函数"""
    print("开始仔细检查单位转换")
    print("="*60)
    check_mne_units()

if __name__ == "__main__":
    main()
